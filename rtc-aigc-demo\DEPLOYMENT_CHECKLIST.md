# 部署检查清单

## 🔧 环境准备

### Python环境
- [ ] Python 3.7+ 已安装
- [ ] pip 已安装并可用
- [ ] 虚拟环境已创建（推荐）

### Node.js环境
- [ ] Node.js 14+ 已安装
- [ ] npm 或 yarn 已安装

## 📦 依赖安装

### Python依赖
```bash
cd python_server
pip install -r requirements.txt
```

检查项：
- [ ] Flask 安装成功
- [ ] Flask-SocketIO 安装成功
- [ ] websockets 安装成功
- [ ] 其他依赖安装成功

### 前端依赖
```bash
npm install
```

检查项：
- [ ] socket.io-client 安装成功
- [ ] @arco-design/web-react 安装成功
- [ ] 其他依赖安装成功

## ⚙️ 配置检查

### API配置
文件：`realtime_dialog/realtime_dialog/config.py`

检查项：
- [ ] X-Api-App-ID 已配置
- [ ] X-Api-Access-Key 已配置
- [ ] X-Api-App-Key 已配置
- [ ] WebSocket URL 正确

### 前端配置
文件：`src/config/index.ts`

检查项：
- [ ] AIGC_PROXY_HOST 指向正确的后端地址（默认：http://localhost:3001）

## 🧪 功能测试

### 快速测试
```bash
python quick_test.py
```

检查项：
- [ ] 依赖检查通过
- [ ] 实时对话模块导入成功
- [ ] 前端依赖检查通过
- [ ] 服务器启动测试通过

### 手动测试
1. **启动后端**
   ```bash
   cd python_server
   python start.py
   ```
   - [ ] 服务器在端口3001启动成功
   - [ ] 无错误日志

2. **启动前端**
   ```bash
   npm start
   ```
   - [ ] 前端在端口3000启动成功
   - [ ] 编译无错误

3. **功能测试**
   - [ ] 访问 http://localhost:3000 成功
   - [ ] 选择"实时语音对话"场景
   - [ ] 连接服务器成功
   - [ ] 开始对话成功
   - [ ] 音频录制功能正常
   - [ ] 文本输入功能正常
   - [ ] 音频播放功能正常

## 🚀 部署步骤

### 开发环境部署
1. [ ] 克隆项目到本地
2. [ ] 安装Python依赖
3. [ ] 安装前端依赖
4. [ ] 配置API密钥
5. [ ] 运行快速测试
6. [ ] 启动后端服务
7. [ ] 启动前端服务
8. [ ] 验证功能正常

### 生产环境部署
1. [ ] 服务器环境准备
2. [ ] 安装依赖
3. [ ] 配置环境变量
4. [ ] 构建前端应用 (`npm run build`)
5. [ ] 配置反向代理（如Nginx）
6. [ ] 启动后端服务（使用进程管理器如PM2）
7. [ ] 配置SSL证书（如需要）
8. [ ] 监控和日志配置

## 🔍 故障排除

### 常见问题

1. **WebSocket连接失败**
   - [ ] 检查后端服务是否启动
   - [ ] 检查端口3001是否被占用
   - [ ] 检查防火墙设置

2. **音频录制失败**
   - [ ] 检查浏览器麦克风权限
   - [ ] 检查HTTPS设置（某些浏览器要求）
   - [ ] 检查音频设备

3. **API调用失败**
   - [ ] 检查API密钥配置
   - [ ] 检查网络连接
   - [ ] 查看后端日志

4. **编译错误**
   - [ ] 检查Node.js版本
   - [ ] 清除node_modules重新安装
   - [ ] 检查TypeScript配置

### 日志检查
- [ ] 后端日志无错误
- [ ] 前端控制台无错误
- [ ] 网络请求正常
- [ ] WebSocket连接稳定

## 📋 性能优化

### 后端优化
- [ ] 使用生产级WSGI服务器（如Gunicorn）
- [ ] 配置适当的工作进程数
- [ ] 启用日志轮转
- [ ] 配置监控

### 前端优化
- [ ] 启用代码分割
- [ ] 优化音频处理性能
- [ ] 配置CDN（如需要）
- [ ] 启用Gzip压缩

## ✅ 部署完成确认

- [ ] 所有功能测试通过
- [ ] 性能测试通过
- [ ] 安全检查通过
- [ ] 监控配置完成
- [ ] 备份策略制定
- [ ] 文档更新完成

## 📞 支持联系

如遇到问题，请检查：
1. README_REALTIME_DIALOG.md 详细文档
2. 项目issue列表
3. 相关日志文件

---

**注意**: 请确保在生产环境中使用HTTPS，并妥善保管API密钥。
