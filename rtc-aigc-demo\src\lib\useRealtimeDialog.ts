/**
 * 实时语音对话React Hook
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import RealtimeDialogClient, { 
  RealtimeDialogConfig, 
  DialogEvent, 
  AudioData 
} from './RealtimeDialogClient';
import AudioManager from './AudioManager';

export interface UseRealtimeDialogOptions {
  config?: RealtimeDialogConfig;
  autoConnect?: boolean;
  autoStartRecording?: boolean;
}

export interface UseRealtimeDialogReturn {
  // 连接状态
  isConnected: boolean;
  isDialogActive: boolean;
  isRecording: boolean;
  
  // 会话信息
  sessionId: string | null;
  userId: string | null;
  
  // 控制方法
  connect: () => Promise<void>;
  disconnect: () => void;
  startDialog: (config?: RealtimeDialogConfig) => void;
  stopDialog: () => void;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  sendText: (text: string, start?: boolean, end?: boolean) => void;
  
  // 事件数据
  lastDialogEvent: DialogEvent | null;
  error: string | null;
}

export const useRealtimeDialog = (options: UseRealtimeDialogOptions = {}): UseRealtimeDialogReturn => {
  const { config = {}, autoConnect = false, autoStartRecording = false } = options;
  
  // 状态管理
  const [isConnected, setIsConnected] = useState(false);
  const [isDialogActive, setIsDialogActive] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [lastDialogEvent, setLastDialogEvent] = useState<DialogEvent | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // 引用管理
  const clientRef = useRef<RealtimeDialogClient | null>(null);
  const audioManagerRef = useRef<AudioManager | null>(null);
  
  // 初始化客户端
  const initializeClient = useCallback(() => {
    if (clientRef.current) {
      return;
    }
    
    clientRef.current = new RealtimeDialogClient(config, {
      onConnected: () => {
        console.log('WebSocket连接成功');
        setIsConnected(true);
        setError(null);
      },
      
      onDisconnected: () => {
        console.log('WebSocket连接断开');
        setIsConnected(false);
        setIsDialogActive(false);
        setIsRecording(false);
      },
      
      onDialogStarted: (data) => {
        console.log('对话已启动:', data);
        setIsDialogActive(true);
        setSessionId(data.session_id);
        setUserId(data.user_id);
        setError(null);
        
        // 自动开始录音
        if (autoStartRecording) {
          startRecording();
        }
      },
      
      onDialogStopped: (data) => {
        console.log('对话已停止:', data);
        setIsDialogActive(false);
        setIsRecording(false);
        if (audioManagerRef.current) {
          audioManagerRef.current.stopRecording();
        }
      },
      
      onAudioData: (data: AudioData) => {
        // 播放接收到的音频数据
        if (audioManagerRef.current) {
          audioManagerRef.current.playHexAudio(data.data).catch(console.error);
        }
      },
      
      onDialogEvent: (event: DialogEvent) => {
        console.log('对话事件:', event);
        setLastDialogEvent(event);
        
        // 处理特定事件
        if (event.event === 450) {
          // 用户开始说话，可能需要停止播放
          console.log('用户开始说话');
        } else if (event.event === 459) {
          // 用户停止说话
          console.log('用户停止说话');
        }
      },
      
      onError: (error) => {
        console.error('实时对话错误:', error);
        setError(error.message);
      }
    });
  }, [config, autoStartRecording]);
  
  // 初始化音频管理器
  const initializeAudioManager = useCallback(async () => {
    if (audioManagerRef.current) {
      return;
    }
    
    try {
      audioManagerRef.current = new AudioManager({
        onAudioData: (data) => {
          // 发送音频数据到服务器
          if (clientRef.current && isDialogActive) {
            clientRef.current.sendAudio(data);
          }
        },
        
        onRecordingStart: () => {
          console.log('开始录音');
          setIsRecording(true);
        },
        
        onRecordingStop: () => {
          console.log('停止录音');
          setIsRecording(false);
        },
        
        onError: (error) => {
          console.error('音频管理器错误:', error);
          setError(error.message);
        }
      });
      
      await audioManagerRef.current.initialize();
      console.log('音频管理器初始化成功');
    } catch (error) {
      console.error('音频管理器初始化失败:', error);
      setError((error as Error).message);
    }
  }, [isDialogActive]);
  
  // 连接方法
  const connect = useCallback(async () => {
    try {
      setError(null);
      initializeClient();
      await initializeAudioManager();
      
      if (clientRef.current) {
        await clientRef.current.connect();
      }
    } catch (error) {
      console.error('连接失败:', error);
      setError((error as Error).message);
    }
  }, [initializeClient, initializeAudioManager]);
  
  // 断开连接方法
  const disconnect = useCallback(() => {
    if (clientRef.current) {
      clientRef.current.disconnect();
      clientRef.current = null;
    }
    
    if (audioManagerRef.current) {
      audioManagerRef.current.cleanup();
      audioManagerRef.current = null;
    }
    
    setIsConnected(false);
    setIsDialogActive(false);
    setIsRecording(false);
    setSessionId(null);
    setUserId(null);
    setLastDialogEvent(null);
  }, []);
  
  // 启动对话方法
  const startDialog = useCallback((dialogConfig?: RealtimeDialogConfig) => {
    if (!clientRef.current || !isConnected) {
      setError('WebSocket未连接');
      return;
    }
    
    try {
      clientRef.current.startDialog(dialogConfig);
    } catch (error) {
      console.error('启动对话失败:', error);
      setError((error as Error).message);
    }
  }, [isConnected]);
  
  // 停止对话方法
  const stopDialog = useCallback(() => {
    if (clientRef.current) {
      clientRef.current.stopDialog();
    }
    
    if (audioManagerRef.current) {
      audioManagerRef.current.stopRecording();
    }
  }, []);
  
  // 开始录音方法
  const startRecording = useCallback(async () => {
    if (!audioManagerRef.current || !isDialogActive) {
      setError('音频管理器未初始化或对话未激活');
      return;
    }
    
    try {
      await audioManagerRef.current.startRecording();
    } catch (error) {
      console.error('开始录音失败:', error);
      setError((error as Error).message);
    }
  }, [isDialogActive]);
  
  // 停止录音方法
  const stopRecording = useCallback(() => {
    if (audioManagerRef.current) {
      audioManagerRef.current.stopRecording();
    }
  }, []);
  
  // 发送文本方法
  const sendText = useCallback((text: string, start: boolean = true, end: boolean = true) => {
    if (!clientRef.current || !isDialogActive) {
      setError('WebSocket未连接或对话未激活');
      return;
    }
    
    try {
      clientRef.current.sendText(text, start, end);
    } catch (error) {
      console.error('发送文本失败:', error);
      setError((error as Error).message);
    }
  }, [isDialogActive]);
  
  // 自动连接
  useEffect(() => {
    if (autoConnect) {
      connect();
    }
    
    // 清理函数
    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);
  
  return {
    // 状态
    isConnected,
    isDialogActive,
    isRecording,
    sessionId,
    userId,
    lastDialogEvent,
    error,
    
    // 方法
    connect,
    disconnect,
    startDialog,
    stopDialog,
    startRecording,
    stopRecording,
    sendText
  };
};

export default useRealtimeDialog;
