/**
 * 实时语音对话WebSocket客户端
 */

import { io, Socket } from 'socket.io-client';
import { AIGC_PROXY_HOST } from '@/config';

export interface DialogEvent {
  event: number;
  payload: any;
  session_id: string;
}

export interface AudioData {
  data: string; // 十六进制字符串
  length: number;
}

export interface RealtimeDialogConfig {
  session_id?: string;
  user_id?: string;
  bot_name?: string;
  system_role?: string;
  speaking_style?: string;
}

export interface RealtimeDialogCallbacks {
  onConnected?: () => void;
  onDisconnected?: () => void;
  onDialogStarted?: (data: { session_id: string; user_id: string }) => void;
  onDialogStopped?: (data: { session_id: string }) => void;
  onAudioData?: (data: AudioData) => void;
  onDialogEvent?: (event: DialogEvent) => void;
  onError?: (error: { message: string }) => void;
}

export class RealtimeDialogClient {
  private socket: Socket | null = null;
  private config: RealtimeDialogConfig = {};
  private callbacks: RealtimeDialogCallbacks = {};
  private isConnected = false;
  private sessionId: string | null = null;

  constructor(config: RealtimeDialogConfig = {}, callbacks: RealtimeDialogCallbacks = {}) {
    this.config = config;
    this.callbacks = callbacks;
  }

  /**
   * 连接到WebSocket服务器
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.socket = io(AIGC_PROXY_HOST, {
          transports: ['websocket'],
          timeout: 10000,
        });

        this.socket.on('connect', () => {
          console.log('WebSocket连接成功');
          this.isConnected = true;
          this.callbacks.onConnected?.();
          resolve();
        });

        this.socket.on('disconnect', () => {
          console.log('WebSocket连接断开');
          this.isConnected = false;
          this.callbacks.onDisconnected?.();
        });

        this.socket.on('connected', (data) => {
          console.log('服务器确认连接:', data);
        });

        this.socket.on('dialog_started', (data) => {
          console.log('对话已启动:', data);
          this.sessionId = data.session_id;
          this.callbacks.onDialogStarted?.(data);
        });

        this.socket.on('dialog_stopped', (data) => {
          console.log('对话已停止:', data);
          this.callbacks.onDialogStopped?.(data);
        });

        this.socket.on('audio_data', (data: AudioData) => {
          this.callbacks.onAudioData?.(data);
        });

        this.socket.on('dialog_event', (event: DialogEvent) => {
          console.log('对话事件:', event);
          this.callbacks.onDialogEvent?.(event);
        });

        this.socket.on('error', (error) => {
          console.error('WebSocket错误:', error);
          this.callbacks.onError?.(error);
          reject(new Error(error.message || '连接失败'));
        });

        this.socket.on('connect_error', (error) => {
          console.error('连接错误:', error);
          this.callbacks.onError?.({ message: error.message || '连接失败' });
          reject(error);
        });

      } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 启动对话
   */
  startDialog(config?: RealtimeDialogConfig): void {
    if (!this.socket || !this.isConnected) {
      throw new Error('WebSocket未连接');
    }

    const dialogConfig = { ...this.config, ...config };
    
    this.socket.emit('start_dialog', {
      session_id: dialogConfig.session_id || this.generateSessionId(),
      user_id: dialogConfig.user_id || this.generateUserId(),
      bot_name: dialogConfig.bot_name || '豆包',
      system_role: dialogConfig.system_role || '你使用活泼灵动的女声，性格开朗，热爱生活。',
      speaking_style: dialogConfig.speaking_style || '你的说话风格简洁明了，语速适中，语调自然。'
    });
  }

  /**
   * 发送音频数据
   */
  sendAudio(audioData: ArrayBuffer | Uint8Array): void {
    if (!this.socket || !this.isConnected || !this.sessionId) {
      throw new Error('WebSocket未连接或对话未启动');
    }

    // 转换为十六进制字符串
    const uint8Array = audioData instanceof ArrayBuffer ? new Uint8Array(audioData) : audioData;
    const hexString = Array.from(uint8Array)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');

    this.socket.emit('send_audio', {
      session_id: this.sessionId,
      audio_data: hexString
    });
  }

  /**
   * 发送文本消息
   */
  sendText(text: string, start: boolean = true, end: boolean = true): void {
    if (!this.socket || !this.isConnected || !this.sessionId) {
      throw new Error('WebSocket未连接或对话未启动');
    }

    this.socket.emit('send_text', {
      session_id: this.sessionId,
      text,
      start,
      end
    });
  }

  /**
   * 停止对话
   */
  stopDialog(): void {
    if (!this.socket || !this.isConnected || !this.sessionId) {
      return;
    }

    this.socket.emit('stop_dialog', {
      session_id: this.sessionId
    });
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.sessionId = null;
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * 获取会话ID
   */
  getSessionId(): string | null {
    return this.sessionId;
  }

  /**
   * 更新回调函数
   */
  updateCallbacks(callbacks: RealtimeDialogCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 生成用户ID
   */
  private generateUserId(): string {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

export default RealtimeDialogClient;
