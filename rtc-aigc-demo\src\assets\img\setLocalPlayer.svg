<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect y="36" width="36" height="36" rx="4" transform="rotate(-90 0 36)" fill="black" fill-opacity="0.4"/>
<g filter="url(#filter0_d_100_31232)">
<rect x="9" y="9" width="18" height="18" rx="3" stroke="white" stroke-width="2"/>
<path d="M19 9H25C26.1046 9 27 9.89543 27 11V17H21C19.8954 17 19 16.1046 19 15V9Z" fill="white" fill-opacity="0.4" stroke="white" stroke-width="2"/>
</g>
<defs>
<filter id="filter0_d_100_31232" x="5" y="5" width="26" height="26" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_100_31232"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_100_31232" result="shape"/>
</filter>
</defs>
</svg>
