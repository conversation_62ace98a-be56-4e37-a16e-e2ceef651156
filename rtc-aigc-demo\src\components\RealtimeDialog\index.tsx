/**
 * 实时语音对话组件
 */

import React, { useState, useEffect } from 'react';
import { Button, Card, Space, Typography, Alert, Input, Switch } from '@arco-design/web-react';
import { IconMicrophone, IconMicrophoneOff, IconSound, IconSoundOff, IconSend } from '@arco-design/web-react/icon';
import useRealtimeDialog from '@/lib/useRealtimeDialog';
import styles from './index.module.less';

const { Text, Paragraph } = Typography;

export interface RealtimeDialogProps {
  className?: string;
  autoConnect?: boolean;
  autoStartRecording?: boolean;
  botName?: string;
  systemRole?: string;
  speakingStyle?: string;
}

const RealtimeDialog: React.FC<RealtimeDialogProps> = ({
  className,
  autoConnect = false,
  autoStartRecording = false,
  botName = '豆包',
  systemRole = '你使用活泼灵动的女声，性格开朗，热爱生活。',
  speakingStyle = '你的说话风格简洁明了，语速适中，语调自然。'
}) => {
  const [textInput, setTextInput] = useState('');
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [eventLog, setEventLog] = useState<string[]>([]);

  const {
    isConnected,
    isDialogActive,
    isRecording,
    sessionId,
    userId,
    lastDialogEvent,
    error,
    connect,
    disconnect,
    startDialog,
    stopDialog,
    startRecording,
    stopRecording,
    sendText
  } = useRealtimeDialog({
    config: {
      bot_name: botName,
      system_role: systemRole,
      speaking_style: speakingStyle
    },
    autoConnect,
    autoStartRecording
  });

  // 监听对话事件并记录日志
  useEffect(() => {
    if (lastDialogEvent) {
      const eventText = `[${new Date().toLocaleTimeString()}] 事件 ${lastDialogEvent.event}: ${JSON.stringify(lastDialogEvent.payload)}`;
      setEventLog(prev => [...prev.slice(-9), eventText]); // 保留最近10条记录
    }
  }, [lastDialogEvent]);

  // 处理连接/断开连接
  const handleConnectionToggle = async () => {
    if (isConnected) {
      disconnect();
    } else {
      await connect();
    }
  };

  // 处理对话开始/停止
  const handleDialogToggle = () => {
    if (isDialogActive) {
      stopDialog();
    } else {
      startDialog();
    }
  };

  // 处理录音开始/停止
  const handleRecordingToggle = async () => {
    if (isRecording) {
      stopRecording();
    } else {
      await startRecording();
    }
  };

  // 发送文本消息
  const handleSendText = () => {
    if (textInput.trim()) {
      sendText(textInput.trim());
      setTextInput('');
    }
  };

  // 获取连接状态颜色
  const getStatusColor = () => {
    if (error) return 'red';
    if (isDialogActive) return 'green';
    if (isConnected) return 'orange';
    return 'gray';
  };

  // 获取状态文本
  const getStatusText = () => {
    if (error) return '错误';
    if (isDialogActive) return '对话中';
    if (isConnected) return '已连接';
    return '未连接';
  };

  return (
    <div className={`${styles.container} ${className || ''}`}>
      <Card 
        title="实时语音对话" 
        className={styles.card}
        extra={
          <div className={styles.status}>
            <div className={`${styles.statusDot} ${styles[getStatusColor()]}`} />
            <Text>{getStatusText()}</Text>
          </div>
        }
      >
        {/* 错误提示 */}
        {error && (
          <Alert
            type="error"
            message={error}
            closable
            className={styles.alert}
          />
        )}

        {/* 会话信息 */}
        {sessionId && (
          <div className={styles.sessionInfo}>
            <Text type="secondary">会话ID: {sessionId}</Text>
            <br />
            <Text type="secondary">用户ID: {userId}</Text>
          </div>
        )}

        {/* 控制按钮 */}
        <div className={styles.controls}>
          <Space size="medium">
            <Button
              type={isConnected ? 'secondary' : 'primary'}
              onClick={handleConnectionToggle}
              loading={false}
            >
              {isConnected ? '断开连接' : '连接服务器'}
            </Button>

            <Button
              type={isDialogActive ? 'secondary' : 'primary'}
              onClick={handleDialogToggle}
              disabled={!isConnected}
            >
              {isDialogActive ? '停止对话' : '开始对话'}
            </Button>

            <Button
              type={isRecording ? 'primary' : 'secondary'}
              icon={isRecording ? <IconMicrophone /> : <IconMicrophoneOff />}
              onClick={handleRecordingToggle}
              disabled={!isDialogActive}
              status={isRecording ? 'success' : 'default'}
            >
              {isRecording ? '停止录音' : '开始录音'}
            </Button>

            <Switch
              checked={isAudioEnabled}
              onChange={setIsAudioEnabled}
              checkedIcon={<IconSound />}
              uncheckedIcon={<IconSoundOff />}
            />
          </Space>
        </div>

        {/* 文本输入 */}
        <div className={styles.textInput}>
          <Input.Group compact>
            <Input
              value={textInput}
              onChange={setTextInput}
              placeholder="输入文本消息..."
              disabled={!isDialogActive}
              onPressEnter={handleSendText}
              style={{ width: 'calc(100% - 80px)' }}
            />
            <Button
              type="primary"
              icon={<IconSend />}
              onClick={handleSendText}
              disabled={!isDialogActive || !textInput.trim()}
              style={{ width: '80px' }}
            >
              发送
            </Button>
          </Input.Group>
        </div>

        {/* 事件日志 */}
        <div className={styles.eventLog}>
          <Text.Bold>事件日志:</Text.Bold>
          <div className={styles.logContent}>
            {eventLog.length === 0 ? (
              <Text type="secondary">暂无事件</Text>
            ) : (
              eventLog.map((log, index) => (
                <div key={index} className={styles.logItem}>
                  <Text code>{log}</Text>
                </div>
              ))
            )}
          </div>
        </div>

        {/* 使用说明 */}
        <div className={styles.instructions}>
          <Paragraph>
            <Text.Bold>使用说明:</Text.Bold>
            <br />
            1. 点击"连接服务器"建立WebSocket连接
            <br />
            2. 点击"开始对话"启动语音对话会话
            <br />
            3. 点击"开始录音"进行语音输入，或直接输入文本
            <br />
            4. AI会实时响应并播放语音回复
          </Paragraph>
        </div>
      </Card>
    </div>
  );
};

export default RealtimeDialog;
