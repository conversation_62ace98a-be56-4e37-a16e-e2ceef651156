#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试脚本 - 验证系统基本功能
"""

import requests
import json
import sys
import os

def test_dependencies():
    """测试依赖是否安装"""
    print("=== 检查依赖 ===")
    
    try:
        import flask
        print(f"✓ Flask: {flask.__version__}")
    except ImportError:
        print("✗ Flask 未安装")
        return False
        
    try:
        import flask_socketio
        print(f"✓ Flask-SocketIO: {flask_socketio.__version__}")
    except ImportError:
        print("✗ Flask-SocketIO 未安装")
        return False
        
    try:
        import websockets
        print(f"✓ websockets: {websockets.__version__}")
    except ImportError:
        print("✗ websockets 未安装")
        return False
        
    return True

def test_realtime_dialog_module():
    """测试实时对话模块是否可用"""
    print("\n=== 检查实时对话模块 ===")
    
    # 添加模块路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    realtime_dialog_path = os.path.join(current_dir, '..', 'realtime_dialog', 'realtime_dialog')
    
    if not os.path.exists(realtime_dialog_path):
        print(f"✗ 实时对话模块路径不存在: {realtime_dialog_path}")
        return False
        
    sys.path.insert(0, realtime_dialog_path)
    
    try:
        import config as dialog_config
        print("✓ config.py 导入成功")
        
        # 检查配置
        if hasattr(dialog_config, 'ws_connect_config'):
            config = dialog_config.ws_connect_config
            if config.get('headers', {}).get('X-Api-App-ID'):
                print("✓ API配置存在")
            else:
                print("⚠ API配置可能不完整，请检查config.py")
        else:
            print("✗ ws_connect_config 配置不存在")
            
    except ImportError as e:
        print(f"✗ 导入config失败: {e}")
        return False
        
    try:
        import protocol
        print("✓ protocol.py 导入成功")
    except ImportError as e:
        print(f"✗ 导入protocol失败: {e}")
        return False
        
    try:
        from realtime_dialog_client import RealtimeDialogClient
        print("✓ RealtimeDialogClient 导入成功")
    except ImportError as e:
        print(f"✗ 导入RealtimeDialogClient失败: {e}")
        return False
        
    return True

def test_frontend_dependencies():
    """测试前端依赖"""
    print("\n=== 检查前端依赖 ===")
    
    # 检查package.json
    if os.path.exists('package.json'):
        print("✓ package.json 存在")
        
        # 检查node_modules
        if os.path.exists('node_modules'):
            print("✓ node_modules 存在")
            
            # 检查关键依赖
            key_deps = ['socket.io-client', '@arco-design/web-react', 'react']
            for dep in key_deps:
                dep_path = os.path.join('node_modules', dep)
                if os.path.exists(dep_path):
                    print(f"✓ {dep} 已安装")
                else:
                    print(f"✗ {dep} 未安装")
        else:
            print("✗ node_modules 不存在，请运行 npm install")
            return False
    else:
        print("✗ package.json 不存在")
        return False
        
    return True

def test_server_startup():
    """测试服务器是否可以启动"""
    print("\n=== 测试服务器启动 ===")
    
    try:
        # 尝试导入应用
        sys.path.insert(0, 'python_server')
        from app import app, socketio
        print("✓ 应用导入成功")
        
        # 测试基本路由
        with app.test_client() as client:
            response = client.get('/api/scenes')
            if response.status_code == 200:
                print("✓ /api/scenes 路由正常")
                data = json.loads(response.data)
                print(f"  场景数量: {len(data.get('data', {}).get('scenes', []))}")
            else:
                print(f"✗ /api/scenes 路由异常: {response.status_code}")
                
    except Exception as e:
        print(f"✗ 服务器启动测试失败: {e}")
        return False
        
    return True

def main():
    """主测试函数"""
    print("实时语音对话系统 - 快速测试")
    print("=" * 50)
    
    all_passed = True
    
    # 测试Python依赖
    if not test_dependencies():
        all_passed = False
        
    # 测试实时对话模块
    if not test_realtime_dialog_module():
        all_passed = False
        
    # 测试前端依赖
    if not test_frontend_dependencies():
        all_passed = False
        
    # 测试服务器启动
    if not test_server_startup():
        all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("✓ 所有测试通过！系统准备就绪。")
        print("\n启动步骤:")
        print("1. 启动后端: python python_server/start.py")
        print("2. 启动前端: npm start")
        print("3. 访问: http://localhost:3000")
    else:
        print("✗ 部分测试失败，请检查上述错误信息。")
        print("\n常见解决方案:")
        print("1. 安装Python依赖: pip install -r python_server/requirements.txt")
        print("2. 安装前端依赖: npm install")
        print("3. 检查实时对话模块路径是否正确")
        print("4. 检查API配置是否完整")

if __name__ == "__main__":
    main()
