#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动脚本 - 同时启动Python后端和前端
"""

import os
import sys
import subprocess
import threading
import time
import signal

def run_python_server():
    """启动Python后端服务器"""
    print("启动Python后端服务器...")
    os.chdir("python_server")
    try:
        subprocess.run([sys.executable, "start.py"], check=True)
    except KeyboardInterrupt:
        print("Python服务器已停止")
    except Exception as e:
        print(f"Python服务器启动失败: {e}")

def run_frontend():
    """启动前端开发服务器"""
    print("启动前端开发服务器...")
    try:
        # 等待后端服务器启动
        time.sleep(3)
        subprocess.run(["npm", "start"], check=True)
    except KeyboardInterrupt:
        print("前端服务器已停止")
    except Exception as e:
        print(f"前端服务器启动失败: {e}")

def signal_handler(sig, frame):
    """信号处理器"""
    print("\n正在停止所有服务...")
    sys.exit(0)

if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    print("=== 实时语音对话系统启动器 ===")
    print("正在启动后端和前端服务...")
    
    # 创建线程启动服务
    backend_thread = threading.Thread(target=run_python_server)
    frontend_thread = threading.Thread(target=run_frontend)
    
    backend_thread.daemon = True
    frontend_thread.daemon = True
    
    # 启动服务
    backend_thread.start()
    frontend_thread.start()
    
    try:
        # 等待线程完成
        backend_thread.join()
        frontend_thread.join()
    except KeyboardInterrupt:
        print("\n正在停止所有服务...")
        sys.exit(0)
