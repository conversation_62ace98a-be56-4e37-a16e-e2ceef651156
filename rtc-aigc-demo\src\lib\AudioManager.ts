/**
 * 音频管理器 - 处理音频录制、播放和格式转换
 */

export interface AudioConfig {
  sampleRate: number;
  channels: number;
  bitDepth: number;
  chunkSize: number;
}

export interface AudioManagerCallbacks {
  onAudioData?: (data: ArrayBuffer) => void;
  onRecordingStart?: () => void;
  onRecordingStop?: () => void;
  onError?: (error: Error) => void;
}

export class AudioManager {
  private audioContext: AudioContext | null = null;
  private mediaStream: MediaStream | null = null;
  private mediaRecorder: MediaRecorder | null = null;
  private workletNode: AudioWorkletNode | null = null;
  private isRecording = false;
  private audioQueue: ArrayBuffer[] = [];
  private playbackQueue: ArrayBuffer[] = [];
  private isPlaying = false;

  private inputConfig: AudioConfig = {
    sampleRate: 16000,
    channels: 1,
    bitDepth: 16,
    chunkSize: 3200
  };

  private outputConfig: AudioConfig = {
    sampleRate: 24000,
    channels: 1,
    bitDepth: 32,
    chunkSize: 3200
  };

  private callbacks: AudioManagerCallbacks = {};

  constructor(callbacks: AudioManagerCallbacks = {}) {
    this.callbacks = callbacks;
  }

  /**
   * 初始化音频系统
   */
  async initialize(): Promise<void> {
    try {
      // 创建音频上下文
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: this.inputConfig.sampleRate
      });

      // 请求麦克风权限
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.inputConfig.sampleRate,
          channelCount: this.inputConfig.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      console.log('音频系统初始化成功');
    } catch (error) {
      console.error('音频系统初始化失败:', error);
      this.callbacks.onError?.(error as Error);
      throw error;
    }
  }

  /**
   * 开始录音
   */
  async startRecording(): Promise<void> {
    if (!this.audioContext || !this.mediaStream) {
      throw new Error('音频系统未初始化');
    }

    if (this.isRecording) {
      return;
    }

    try {
      // 恢复音频上下文
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      // 创建音频源
      const source = this.audioContext.createMediaStreamSource(this.mediaStream);
      
      // 创建处理器节点
      const processor = this.audioContext.createScriptProcessor(this.inputConfig.chunkSize, 1, 1);
      
      processor.onaudioprocess = (event) => {
        if (!this.isRecording) return;

        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0);
        
        // 转换为16位PCM
        const pcmData = this.float32ToPCM16(inputData);
        
        // 发送音频数据
        this.callbacks.onAudioData?.(pcmData.buffer);
      };

      // 连接音频节点
      source.connect(processor);
      processor.connect(this.audioContext.destination);

      this.isRecording = true;
      this.callbacks.onRecordingStart?.();
      
      console.log('开始录音');
    } catch (error) {
      console.error('开始录音失败:', error);
      this.callbacks.onError?.(error as Error);
      throw error;
    }
  }

  /**
   * 停止录音
   */
  stopRecording(): void {
    if (!this.isRecording) {
      return;
    }

    this.isRecording = false;
    this.callbacks.onRecordingStop?.();
    
    console.log('停止录音');
  }

  /**
   * 播放音频数据
   */
  async playAudio(audioData: ArrayBuffer): Promise<void> {
    if (!this.audioContext) {
      throw new Error('音频系统未初始化');
    }

    try {
      // 恢复音频上下文
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      // 将字节数据转换为音频数据
      const uint8Array = new Uint8Array(audioData);
      const float32Array = this.pcm32ToFloat32(uint8Array);

      // 创建音频缓冲区
      const audioBuffer = this.audioContext.createBuffer(
        this.outputConfig.channels,
        float32Array.length,
        this.outputConfig.sampleRate
      );

      audioBuffer.copyToChannel(float32Array, 0);

      // 创建音频源并播放
      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(this.audioContext.destination);
      source.start();

    } catch (error) {
      console.error('播放音频失败:', error);
      this.callbacks.onError?.(error as Error);
      throw error;
    }
  }

  /**
   * 播放十六进制音频数据
   */
  async playHexAudio(hexData: string): Promise<void> {
    try {
      // 将十六进制字符串转换为字节数组
      const bytes = new Uint8Array(hexData.length / 2);
      for (let i = 0; i < hexData.length; i += 2) {
        bytes[i / 2] = parseInt(hexData.substr(i, 2), 16);
      }
      
      await this.playAudio(bytes.buffer);
    } catch (error) {
      console.error('播放十六进制音频失败:', error);
      this.callbacks.onError?.(error as Error);
      throw error;
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopRecording();
    
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }
    
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    console.log('音频管理器已清理');
  }

  /**
   * 获取录音状态
   */
  getRecordingStatus(): boolean {
    return this.isRecording;
  }

  /**
   * 更新回调函数
   */
  updateCallbacks(callbacks: AudioManagerCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Float32转PCM16
   */
  private float32ToPCM16(float32Array: Float32Array): Int16Array {
    const pcm16Array = new Int16Array(float32Array.length);
    for (let i = 0; i < float32Array.length; i++) {
      const sample = Math.max(-1, Math.min(1, float32Array[i]));
      pcm16Array[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
    }
    return pcm16Array;
  }

  /**
   * PCM32转Float32
   */
  private pcm32ToFloat32(uint8Array: Uint8Array): Float32Array {
    const float32Array = new Float32Array(uint8Array.length / 4);
    const dataView = new DataView(uint8Array.buffer);
    
    for (let i = 0; i < float32Array.length; i++) {
      float32Array[i] = dataView.getFloat32(i * 4, true); // little endian
    }
    
    return float32Array;
  }

  /**
   * 检查浏览器支持
   */
  static checkSupport(): boolean {
    return !!(
      navigator.mediaDevices &&
      navigator.mediaDevices.getUserMedia &&
      (window.AudioContext || (window as any).webkitAudioContext)
    );
  }
}

export default AudioManager;
