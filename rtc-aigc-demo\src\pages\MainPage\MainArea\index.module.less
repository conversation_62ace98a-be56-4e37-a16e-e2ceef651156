/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

.wrapper {
  width: 100%;
  height: 100%;
  background-color: white;
  border: 1px solid var(--line-color-border-2, rgba(234, 237, 241, 1));
  border-radius: 16px;
  padding: 20px 12.5%;

  .space {
    width: 100%;
    min-height: 40px;
  }

  .doubaoIcon {
    width: 111px;
    height: 111px;
    min-height: 111px;
    overflow: hidden;
  }

  .interruptTag {
    width: max-content;
    height: 22px;
    padding: 0px 6px 0px 6px;
    border-radius: 4px;
    margin-left: 4px;
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0.003em;
    color: var(--text-color-text-3, rgba(115, 122, 135, 1));
    background: var(--security-unknown-tag-unknown-1, rgba(241, 243, 245, 1));
  }

  .welcome {
    font-family: PingFang SC;
    font-size: 24px;
    font-weight: 500;
    line-height: 32px;
    letter-spacing: 0.003em;
    text-align: left;
    margin-top: 8px;
  }

  .weight {
    background: linear-gradient(90deg, #004FFF 38.86%, #9865FF 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  .tip {
    font-family: PingFang SC;
    font-size: 13px;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0.003em;
    text-align: left;
    color: rgba(27, 30, 61, 0.6);
    margin-top: 18px;
    margin-bottom: 18px;
  }

  .tagProblem {
    width: max-content;
    border-radius: 4px;
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.003em;
    text-align: center;
    margin-bottom: 12px;
    color: rgba(66, 70, 78, 1);
  }

  .conversation {
    overflow-x: hidden;
    overflow-y: auto;
    width: 100%;
    position: relative;
    height: calc(75% - 12px);
    display: flex;
    flex-direction: column;
    padding-bottom: 12px;

    .aiReadying {
      font-family: PingFang SC;
      font-size: 16px;
      font-weight: 500;
      line-height: 18px;
      letter-spacing: 0.003em;
      color: rgba(27, 30, 61, 0.6);
      margin-top: 12px;
      text-align: center;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }

    .aiReading-spin {
      margin-right: 12px;
    }
  }

  .conversation::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }
  
  .conversation::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0);
    border-radius: 0px;
  }
  
  .conversation::-webkit-scrollbar-track {
    background: rgba(0,0,0,0);
    border-radius: 0px;
  }

  .sentence {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
  }
  .user {
    width: max-content;
    border: 0px solid;
    align-self: flex-end;
    padding: 8px 12px 8px 12px;
    border-radius: 12px 0px 12px 12px;
    background: var(--background-color-bg-5, rgba(241, 243, 245, 1));
    margin-top: 12px;
  }
  .robot {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.003em;

    border: 0px solid;
    align-self: flex-start;
    padding: 3px 12px 3px 0px;
  }

  .userTalkingWave {
    height: 100px;
  }

  .userStopTalkingWave {
    height: 100px;
    transform: scaleY(.5);
  }

  .status {
    overflow: hidden;
    width: 100%;
    height: 25%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;

    .status-row {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      .status-icon {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }

      .status-text {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        letter-spacing: 0.003em;
      }
    }

    .desc {
      font-family: PingFang SC;
      font-size: 10px;
      font-weight: 400;
      line-height: 18px;
      letter-spacing: 0.003em;
      text-align: center;
      color: var(--text-color-text-4, rgba(199, 204, 214, 1));
    }

    .micNotify {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
    }

    .micReopen {
      position: relative;
      width: 107px;
      height: 40px;
      padding: 5px 16px 5px 16px;
      margin-left: 12px;
      margin-right: 12px;
      background-clip: padding-box; /* 确保背景不覆盖边框 */
      border-radius: 12px;

      &:hover,
      &:active,
      &:focus {
        opacity: 1;
        color: rgba(0, 0, 0, 0.85);
        border-color: #d9d9d9;
      }
    }
  }

  .interrupt {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 12px;
    width: max-content;
    line-height: 28px;
    padding: 1px 6px 1px 6px;
    border-radius: 4px;
    margin-left: 4px;
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0.003em;
    text-align: left;
    box-shadow: 0px 0px 0px 1px rgba(221, 226, 233, 1);
    color: var(--text-color-text-3, rgba(115, 122, 135, 1));

    &:hover,
    &:active,
    &:focus {
      opacity: 1;
      border-color: #d9d9d9;
    }

    img {
      margin-right: 8px;
    }
  }
}