{"name": "tarot", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate-sitemap": "node scripts/generateSitemap.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fingerprintjs/fingerprintjs": "^4.6.2", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@mui/material": "^6.4.7", "@paypal/react-paypal-js": "^8.8.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@react-oauth/google": "^0.12.1", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@react-three/rapier": "^1.5.0", "@types/crypto-js": "^4.2.2", "@types/dompurify": "^3.2.0", "@types/next": "^9.0.0", "@types/nodemailer": "^6.4.17", "@types/react-datepicker": "^7.0.0", "@types/react-lazy-load-image-component": "^1.6.4", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.34", "@types/three": "^0.173.0", "@types/uuid": "^10.0.0", "alipay-sdk": "^4.13.0", "antd": "^5.25.1", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dompurify": "^3.2.3", "dotenv": "^16.4.7", "es-module-shims": "1.8.0", "framer-motion": "^11.18.2", "geoip-lite": "^1.4.10", "gsap": "^3.12.7", "html2canvas": "^1.4.1", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.2", "i18next-http-backend": "^3.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.474.0", "marked": "^16.0.0", "meshline": "^3.3.1", "mysql2": "^3.12.0", "next": "^15.1.3", "nodemailer": "^6.9.16", "ogl": "^1.0.11", "openai": "^4.93.0", "postcss": "^8.4.49", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-datepicker": "^7.5.0", "react-day-picker": "^9.5.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.5.1", "react-i18next": "^15.4.0", "react-icons": "^5.4.0", "react-lazy-load-image-component": "^1.6.3", "react-markdown": "^9.0.1", "react-mobile-datepicker": "^4.0.2", "react-responsive": "^10.0.1", "react-router-dom": "^7.0.2", "react-tsparticles": "^2.12.0", "remark-gfm": "^4.0.0", "sharp": "^0.33.5", "sonner": "^2.0.5", "split-type": "^0.3.4", "styled-components": "^6.1.15", "swiper": "^11.2.8", "tailwind-merge": "^3.0.1", "tailwindcss": "^3.4.16", "tailwindcss-animate": "^1.0.7", "three": "^0.167.1", "tsparticles": "^2.12.0", "tsparticles-engine": "2.12.0", "uuid": "^11.0.4", "yarn": "^1.22.22"}, "devDependencies": {"@eslint/js": "^9.15.0", "@tailwindcss/typography": "^0.5.15", "@types/html2canvas": "^1.0.0", "@types/node": "^20.17.10", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "ngrok": "5.0.0-beta.2", "react-snap": "^1.23.0", "tailwind-scrollbar": "^4.0.1", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}}