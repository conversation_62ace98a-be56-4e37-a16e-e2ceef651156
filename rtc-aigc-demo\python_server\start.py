#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动脚本
"""

import os
import sys

# 添加实时对话模块到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
realtime_dialog_path = os.path.join(current_dir, '..', '..', 'realtime_dialog', 'realtime_dialog')
sys.path.insert(0, realtime_dialog_path)

# 启动应用
if __name__ == '__main__':
    from app import app, socketio
    print("启动实时语音对话服务器...")
    print("服务器地址: http://localhost:3001")
    socketio.run(app, host='0.0.0.0', port=3001, debug=True)
