{"name": "AIGCServer", "version": "1.0.0", "description": "Server for demo to call open api", "main": "app.js", "license": "BSD-3-<PERSON><PERSON>", "private": true, "dependencies": {"@volcengine/openapi": "^1.22.0", "koa": "^2.15.3", "koa-bodyparser": "^4.4.1", "koa2-cors": "^2.0.6", "lodash": "^4.17.21", "node-fetch": "^2.3.2", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}, "scripts": {"dev": "nodemon app.js", "start": "nodemon app.js"}}