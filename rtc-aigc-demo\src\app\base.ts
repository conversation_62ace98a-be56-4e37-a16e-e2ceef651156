/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

import { Message } from '@arco-design/web-react';
import { AIGC_PROXY_HOST } from '@/config';
import type { RequestResponse, ApiConfig, ApiNames, Apis } from './type';

type Headers = Record<string, string>;

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends Array<infer U>
    ? Array<DeepPartial<U>>
    : T[P] extends object
    ? DeepPartial<T[P]>
    : T[P];
};

/**
 * @brief Get
 * @param apiPath
 * @param headers
 */
export const requestGetMethod = ({
  apiPath,
  headers = {},
}: {
  apiPath: string;
  headers?: Record<string, string>;
}) => {
  return async (params: Record<string, any> = {}) => {
    const queryString = Object.keys(params)
      .map((key) => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    const url = `${AIGC_PROXY_HOST}${apiPath}${queryString ? `?${queryString}` : ''}`;
    const res = await fetch(url, {
      headers: {
        ...headers,
      },
    });
    return res;
  };
};

/**
 * @brief Post
 */
export const requestPostMethod = ({
  apiPath,
  isJson = true,
  headers = {},
}: {
  apiPath: string;
  isJson?: boolean;
  headers?: Headers;
}) => {
  return async <T>(params: T) => {
    const res = await fetch(`${AIGC_PROXY_HOST}${apiPath}`, {
      method: 'post',
      headers: {
        'content-type': 'application/json',
        ...headers,
      },
      body: (isJson ? JSON.stringify(params) : params) as BodyInit,
    });
    return res;
  };
};

/**
 * @brief Return handler - 更新为新的响应格式
 * @param res
 */
export const resultHandler = (res: any) => {
  // 新的API响应格式
  if (res && typeof res === 'object') {
    if (res.success === false) {
      const errorMessage = res.error || '请求失败';
      Message.error(`请求失败: ${errorMessage}`);
      throw new Error(`API调用失败: ${errorMessage}`);
    }

    // 返回数据部分，如果有data字段则返回data，否则返回整个响应
    return res.data || res;
  }

  // 兼容旧格式
  const { Result, ResponseMetadata } = res || {};
  if (ResponseMetadata?.Error) {
    Message.error(
      `[${ResponseMetadata?.Action}]call failed(reason: ${ResponseMetadata.Error?.Message})`
    );
    throw new Error(
      `[${ResponseMetadata?.Action}]call failed(${JSON.stringify(ResponseMetadata, null, 2)})`
    );
  }

  return Result || res;
};

/**
 * @brief Generate APIs by apiConfigs
 * @param apiConfigs
 */
export const generateAPIs = <T extends readonly ApiConfig[]>(apiConfigs: T) =>
  apiConfigs.reduce<Apis<T>>((store, cur) => {
    const { action, apiPath = '', method = 'get' } = cur;

    const actionKey = action as ApiNames<T>;
    store[actionKey] = async (params) => {
      const queryData =
        method === 'get'
          ? await requestGetMethod({ apiPath })(params)
          : await requestPostMethod({ apiPath })(params);
      const res = await queryData?.json();
      return resultHandler(res);
    };
    return store;
  }, {} as Apis<T>);
