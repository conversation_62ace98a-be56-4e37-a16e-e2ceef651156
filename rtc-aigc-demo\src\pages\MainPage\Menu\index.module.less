/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

 .wrapper {
  width: 210px;
  height: 100%;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .info {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .title {
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #0c0d0e;
    }

    .desc {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      line-height: 20px;
      color: #737a87;

      :global {
        div.arco-typography, p.arco-typography {
          margin-bottom: 0px;
        }
      }
    }
    .bold {
      font-size: 13px;
      font-weight: 500;
      line-height: 22px;
      color: var(--text-color-text-1, rgba(12, 13, 14, 1));
    }

    .gray {
      display: flex;
      flex-direction: row;
      align-items: center;
      font-size: 13px;
      font-weight: 400;
      line-height: 22px;
      color: var(--text-color-text-3, rgba(115, 122, 135, 1));

      .value {
        width: 65%;
        font-size: 12px;
        font-weight: 500;
        margin-left: 5px;
      }

      :global {
        .arco-typography {
          margin-bottom: 0px;
          display: inline-block;
          color: #737a87;
        }
      }
    }

    .buttonArea {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-top: 8px;

      .getMore {
        width: 100%;
        color: #fff;
        height: 36px;
        text-shadow: none;
        box-shadow: none;
        border: none;
        text-align: center;
        background: linear-gradient(56.59deg, #3c73ff 15.53%, #6e41ee 62.28%, #d641ee 90.32%),
          radial-gradient(
            203.56% 121.74% at 27.12% -21.74%,
            rgba(82, 182, 255, 0.2) 0%,
            rgba(143, 65, 238, 0) 100%
          ),
          radial-gradient(
            134.75% 51.95% at 26.69% 5.8%,
            rgba(157, 214, 255, 0.1) 0%,
            rgba(143, 65, 238, 0) 100%
          ),
          radial-gradient(
            82.39% 83.92% at 147.46% 76.45%,
            rgba(82, 99, 255, 0.8) 0%,
            rgba(143, 65, 238, 0) 100%
          );
        border-radius: 6px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;

        color: var(--Primary-Neutral-0, #fff);
        text-align: center;

        /* Body/body-2 medium */
        font-family: 'PingFang SC';
        font-size: 13px;
        font-style: normal;
        font-weight: 500;
        cursor: pointer;
      }

      .getMore:hover {
        opacity: 0.9;
      }

      .getMore:active {
        opacity: 1;
      }

      .getMore[disabled],
      .getMore[disabled]:hover {
        color: #fff;
        background: linear-gradient(95.87deg, #1664ff 0%, #8040ff 97.7%);
        opacity: 0.8;
      }
    }
  }

  .questions {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .title {
      font-size: 13px;
      font-weight: 500;
      line-height: 22px;
    }

    .line {
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      color: rgba(66, 70, 78, 1);
      cursor: pointer;
    }
  }

  .device {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .box {
    position: relative;
    width: 100%;
    border-radius: 16px;
    background-color: white;
    border: 1px solid var(--line-color-border-2, rgba(234, 237, 241, 1));
    padding: 16px 24px 16px 24px;
    box-sizing: border-box;
    margin-bottom: 16px;
  }

  .resetTime {
    position: relative;
    width: 100%;
    border-radius: 16px;
    padding: 0px 24px 8px 24px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    .normalLine {
      color: #42464e;
      /* Body/body-1 regular */
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
      letter-spacing: 0.036px;
      opacity: 0.8;
    }
  }

  .tagWrapper {
    margin-top: 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
}

.mobile-camera-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;

  .mobile-camera {
    position: relative !important;
    width: 100% !important;
    height: 100% !important;
    top: auto !important;
    right: auto !important;
  }
}
