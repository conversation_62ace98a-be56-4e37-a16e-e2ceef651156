const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');
const nodemailer = require('nodemailer');
const schedule = require('node-schedule');
const { User } = require('./models/User');
const { initializePool } = require('./services/database');
const { trackApiUsage } = require('./middleware/stats');
const EmailService = require('./services/emailService');
const sessionRoutes = require('./routes/session');
const statsRoutes = require('./routes/stats');
const { i18nMiddleware } = require('./i18n');
const path = require('path');
const { initTtsCacheCleanup } = require('./services/ttsCacheCleanup');
const { initVipStatusCleanup } = require('./services/vipStatusCleanup');
const { ReaderVote } = require('./models/ReaderVote');
const { initBaiduUrlPushTask } = require('./tasks/baiduUrlPush');
const fs = require('fs');

// 先加载环境变量
dotenv.config();

// 确保环境变量加载后再初始化邮箱配置
// console.log('正在初始化邮箱配置...');
EmailService.initEmailConfigs();

// 验证邮件配置，但不创建传输器
// console.log('邮件配置已加载，共有 ' + EmailService.emailConfigs.length + ' 个可用邮箱');

// 异步验证所有邮箱配置
(async () => {
  try {
    await EmailService.verifyAllConfigs();
  } catch (error) {
    console.error('验证邮箱配置时出错:', error);
  }
})();

// 初始化TTS缓存清理服务，每小时执行一次（北京时间整点）
initTtsCacheCleanup('0 0 * * * *');

// 初始化VIP状态清理服务，每天执行一次（北京时间午夜）
initVipStatusCleanup('0 0 0 * * *');

// 初始化百度URL推送定时任务
initBaiduUrlPushTask();

// 检查生产环境并执行星座运势生成任务
const checkProductionAndGenerateHoroscopes = async () => {
  // 检查是否为生产环境（通过检查目录是否存在）
  const productionDir = '/var/www/tarot';

  if (fs.existsSync(productionDir)) {
    console.log('检测到生产环境，开始执行星座运势生成任务...');

    try {
      // 导入必要的模块
      const { HOROSCOPE_TYPES, ZODIAC_SIGNS, getHoroscope, formatDateForDb } = require('./services/horoscopeService');
      const { cleanupOldFiles, getAllHoroscopesFromFile } = require('./services/horoscopeFileService');
      const { ensureHoroscopeTableExists } = require('./services/horoscopeDbService');

      // 确保数据库表结构正确
      try {
        await ensureHoroscopeTableExists();
        console.log('数据库表结构已准备就绪');
      } catch (dbError) {
        console.error('初始化数据库表结构失败:', dbError);
        console.log('继续执行，数据将只保存到文件...');
      }

      // 检查星座运势生成结果，如果有失败的情况则发送提醒邮件
      const checkHoroscopeGeneration = async (type, date, language) => {
        try {
          console.log(`检查 ${language} ${type} 运势生成结果...`);

          // 从本地文件获取所有星座的运势数据
          const horoscopes = await getAllHoroscopesFromFile(type, date, language);

          // 检查是否所有星座都有数据
          const missingZodiacs = [];
          for (const sign of ZODIAC_SIGNS) {
            if (!horoscopes || !horoscopes[sign]) {
              missingZodiacs.push(sign);
            }
          }

          // 如果有缺失的星座数据，发送提醒邮件
          if (missingZodiacs.length > 0) {
            console.warn(`${language} ${type} 运势生成不完整，缺少以下星座: ${missingZodiacs.join(', ')}`);

            // 获取管理员邮箱，使用ADMIN_EMAILS环境变量
            const adminEmails = process.env.ADMIN_EMAILS || '<EMAIL>';

            // 构建邮件内容
            const formattedDate = formatDateForDb(date);
            const emailSubject = `星座运势生成失败提醒 - ${language} ${type} ${formattedDate}`;
            const emailHtml = `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
                <h2 style="color: #d9534f; margin-top: 0;">星座运势生成失败提醒</h2>
                <p>系统检测到部分星座运势生成失败，详情如下：</p>

                <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px;">
                  <p><strong>运势类型:</strong> ${type}</p>
                  <p><strong>语言:</strong> ${language}</p>
                  <p><strong>目标日期:</strong> ${formattedDate}</p>
                  <p><strong>缺失星座:</strong> ${missingZodiacs.join(', ')}</p>
                </div>

                <p>请检查API调用日志和本地文件，确认失败原因并手动重新生成。</p>

                <hr style="margin: 20px 0; border: 0; border-top: 1px solid #eee;">
                <p style="color: #777; font-size: 12px;">此邮件由系统自动发送，请勿直接回复。</p>
              </div>
            `;

            // 发送提醒邮件
            try {
              await EmailService.sendMailWithRetry({
                to: adminEmails,
                subject: emailSubject,
                html: emailHtml
              });
              console.log(`已发送提醒邮件至 ${adminEmails}`);
            } catch (emailError) {
              console.error('发送提醒邮件失败:', emailError);
            }

            return false;
          }

          console.log(`${language} ${type} 所有星座运势生成成功!`);
          return true;
        } catch (error) {
          console.error(`检查星座运势生成结果失败:`, error);
          return false;
        }
      };

      // 为指定类型生成运势的函数
      const generateHoroscopesForAllSigns = async (type, date, language) => {
        try {
          // 只需调用一次getHoroscope，它会生成并保存所有星座的运势
          await getHoroscope(type, ZODIAC_SIGNS[0], date, language);

          // 添加检查逻辑，验证所有星座的数据是否都已成功生成
          await checkHoroscopeGeneration(type, date, language);

          return true;
        } catch (error) {
          console.error(`生成 ${language} ${type} 运势失败:`, error);
          return false;
        }
      };

      // 为指定类型和语言生成最新三个周期的运势
      const generateLatestThreeForType = async (type, language) => {
        console.log(`开始生成 ${language} ${type} 运势...`);

        const today = new Date();
        let dates = [];
        const delay = 3000; // 3秒延迟

        if (type === HOROSCOPE_TYPES.DAILY) {
          // 每日运势：昨天、今天、明天
          const yesterday = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate() - 1));
          const tomorrow = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate() + 1));
          dates = [yesterday, today, tomorrow];
        } else if (type === HOROSCOPE_TYPES.WEEKLY) {
          // 每周运势：上周一、本周一、下周一
          const thisMonday = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), today.getUTCDate() - today.getUTCDay() + 1));
          const lastMonday = new Date(Date.UTC(thisMonday.getUTCFullYear(), thisMonday.getUTCMonth(), thisMonday.getUTCDate() - 7));
          const nextMonday = new Date(Date.UTC(thisMonday.getUTCFullYear(), thisMonday.getUTCMonth(), thisMonday.getUTCDate() + 7));
          dates = [lastMonday, thisMonday, nextMonday];
        } else if (type === HOROSCOPE_TYPES.MONTHLY) {
          // 每月运势：上月、本月、下月
          const thisMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), 1));
          const lastMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth() - 1, 1));
          const nextMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth() + 1, 1));
          dates = [lastMonth, thisMonth, nextMonth];
        } else if (type === HOROSCOPE_TYPES.YEARLY) {
          // 年度运势：去年、今年、明年
          const thisYear = new Date(Date.UTC(today.getUTCFullYear(), 0, 1));
          const lastYear = new Date(Date.UTC(today.getUTCFullYear() - 1, 0, 1));
          const nextYear = new Date(Date.UTC(today.getUTCFullYear() + 1, 0, 1));
          dates = [lastYear, thisYear, nextYear];
        } else if (type === HOROSCOPE_TYPES.LOVE) {
          // 爱情运势：上月、本月、下月
          const thisMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth(), 1));
          const lastMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth() - 1, 1));
          const nextMonth = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth() + 1, 1));
          dates = [lastMonth, thisMonth, nextMonth];
        }

        // 为每个日期生成运势
        for (const date of dates) {
          await generateHoroscopesForAllSigns(type, date, language);
          // 添加延迟，避免API调用过于频繁
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        // 清理旧文件
        try {
          console.log(`清理 ${language} ${type} 旧文件...`);
          await cleanupOldFiles(type, language);
          console.log(`${language} ${type} 旧文件清理完成`);
        } catch (error) {
          console.error(`清理 ${language} ${type} 旧文件失败:`, error);
        }

        console.log(`${language} ${type} 运势生成完成！`);
      };

      // 执行所有任务
      const languages = ['zh-CN', 'en', 'ja', 'zh-TW'];
      const types = [
        HOROSCOPE_TYPES.DAILY,
        HOROSCOPE_TYPES.WEEKLY,
        HOROSCOPE_TYPES.MONTHLY,
        HOROSCOPE_TYPES.YEARLY,
        HOROSCOPE_TYPES.LOVE
      ];

      // 遍历所有需要处理的语言
      for (const language of languages) {
        // 遍历所有需要处理的运势类型
        for (const type of types) {
          await generateLatestThreeForType(type, language);
        }
      }

      console.log('生产环境星座运势生成任务完成！');
    } catch (error) {
      console.error('生产环境星座运势生成任务失败:', error);

      // 发送错误提醒邮件
      try {
        const adminEmails = process.env.ADMIN_EMAILS || '<EMAIL>';
        await EmailService.sendMailWithRetry({
          to: adminEmails,
          subject: '生产环境星座运势生成任务失败',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
              <h2 style="color: #d9534f; margin-top: 0;">星座运势生成任务失败</h2>
              <p>生产环境启动时的星座运势生成任务执行失败，错误详情：</p>

              <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 4px; overflow-x: auto;">
                <pre style="margin: 0;">${error.message}\n\n${error.stack}</pre>
              </div>

              <p>请检查服务器日志获取更多信息，并手动执行星座运势生成任务。</p>

              <hr style="margin: 20px 0; border: 0; border-top: 1px solid #eee;">
              <p style="color: #777; font-size: 12px;">此邮件由系统自动发送，请勿直接回复。</p>
            </div>
          `
        });
        console.log(`已发送错误提醒邮件至 ${adminEmails}`);
      } catch (emailError) {
        console.error('发送错误提醒邮件失败:', emailError);
      }
    }
  } else {
    console.log('非生产环境，跳过星座运势生成任务');
  }
};

// 检查生产环境并执行星座运势生成任务
checkProductionAndGenerateHoroscopes();

// 初始化星座运势生成定时任务
const { scheduleHoroscopeTasks } = require('./tasks/generateHoroscopes');
scheduleHoroscopeTasks();

// 创建表并初始化服务
const initTables = async () => {
  try {
    // 确保reader_votes表已创建
    await ReaderVote.createTable();
    // console.log('Reader votes table initialized');
  } catch (error) {
    console.error('Error initializing tables:', error);
  }
};

const app = express();

// CORS 配置
app.use(cors({
  origin: process.env.NODE_ENV === 'development' 
    ? function(origin, callback) {
        callback(null, origin); // 允许任何来源访问
      }
    : ['https://tarotqa.com'],
  credentials: true
}));

// 添加安全头部，防止点击劫持攻击
app.use((req, res, next) => {
  // 设置X-Frame-Options头，防止网站被嵌入到iframe中
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  // 设置Content-Security-Policy头，提供额外的安全保护
  res.setHeader('Content-Security-Policy', "frame-ancestors 'self'");
  next();
});

// 移除 COOP 和其他可能阻止 postMessage 的安全头
app.use((req, res, next) => {
  res.removeHeader('Cross-Origin-Opener-Policy');
  next();
});

app.use(express.json());
app.use(trackApiUsage);
app.use(i18nMiddleware);

// 提供静态文件服务
app.use(express.static('public'));
app.use('/temp', express.static(path.join(__dirname, 'public/temp')));

// Initialize MySQL connection pool
initializePool()
  .then(() => {
    console.log('Connected to MySQL database');
    // 初始化数据库表
    return initTables();
  })
  .catch(err => {
    console.error('Could not connect to MySQL:', err);
    process.exit(1);
  });

// 输出已加载的邮箱配置信息，不需要重新初始化
// console.log(`已加载 ${EmailService.emailConfigs.length} 个邮箱配置`);

// Import routes
const tarotRouter = require('./routes/tarot');
const readingRouter = require('./routes/reading');
const blogReadingRouter = require('./routes/blog-reading-api');
const spreadRecommendationRouter = require('./routes/spreadRecommendation');
const authRoutes = require('./routes/auth');
const followupRoutes = require('./routes/followup');
const deepAnalysisRoutes = require('./routes/deepanalysis');
const fortuneRoutes = require('./routes/fortune');
const invitationRoutes = require('./routes/invitation');
const userRoutes = require('./routes/user');
const readingFeedbackRoutes = require('./routes/reading-feedback');
const ttsRoutes = require('./routes/tts');
const proTtsRoutes = require('./routes/pro-tts');
const ttsStatisticsRoutes = require('./routes/tts-statistics');
const readerRoutes = require('./routes/reader');
const baiduPushRoutes = require('./routes/baiduPush');
const blogReadingsDataRoutes = require('./routes/blog-readings-data');
const yesNoTarotRoutes = require('./routes/yes-no-tarot');
const horoscopeRoutes = require('./routes/horoscope');
// 删除voice-chat模块导入

// Use routes
app.use('/api/tarot', tarotRouter);
app.use('/api/reading', readingRouter);
app.use('/api/auth', authRoutes);
app.use('/api/session', sessionRoutes);
app.use('/api/stats', statsRoutes);
app.use('/api/followup', followupRoutes);
app.use('/api/payment', require('./src/routes/paymentRoutes'));
app.use('/api/spread-recommendation', spreadRecommendationRouter);
app.use('/api/deep-analysis', deepAnalysisRoutes);
app.use('/api/fortune', fortuneRoutes);
app.use('/api/invitation', invitationRoutes);
app.use('/api/feedback', require('./routes/feedback'));
app.use('/api/user', userRoutes);
app.use('/api/reading-feedback', readingFeedbackRoutes);
app.use('/api/tts', ttsRoutes);
app.use('/api/pro-tts', proTtsRoutes);
app.use('/api/tts-statistics', ttsStatisticsRoutes);
app.use('/api/reader', readerRoutes);
app.use('/api/seo', baiduPushRoutes);
app.use('/api/blog-reading', blogReadingRouter);
app.use('/api/fingerprint', blogReadingsDataRoutes);
app.use('/api/yes-no-tarot', yesNoTarotRoutes);
app.use('/api/horoscope', horoscopeRoutes);
// 删除voice-chat路由配置

// 添加不带/api前缀的TTS路由支持（兼容新的前端请求）
app.use('/tts', ttsRoutes);
app.use('/pro-tts', proTtsRoutes);
app.use('/blog-reading', blogReadingRouter);
app.use('/fingerprint', blogReadingsDataRoutes);
app.use('/auth', authRoutes);  // 添加不带/api前缀的auth路由

// 设置每日统计数据更新和发送邮件任务
const updateDailyStats = async () => {
  const StatsService = require('./services/statsService');
  try {
    console.log('Starting daily stats update...');
    
    // 更新统计数据
    await StatsService.updateUserStats();
    console.log('Daily stats updated successfully');
    
    // 发送统计报告邮件
    await EmailService.sendStatsReport(process.env.ADMIN_EMAILS || process.env.EMAIL_USER);
    console.log('Daily stats report sent successfully');
  } catch (error) {
    console.error('Error in daily stats update:', error);
  }
};

// 每天早上 8:00 运行统计更新和发送邮件
// Cron 格式: '秒 分 时 日 月 星期'
schedule.scheduleJob('0 0 8 * * *', updateDailyStats);

// 启动时也运行一次统计更新（但不发送邮件）
const initializeStats = async () => {
  const StatsService = require('./services/statsService');
  try {
    await StatsService.updateUserStats();
    // console.log('Initial stats update completed');
  } catch (error) {
    console.error('Error in initial stats update:', error);
  }
};

initializeStats();
// 确保公共目录可以被访问
// 固定指向生产环境持久化目录
app.use('/horoscopes', express.static('/var/www/tarot/horoscopes'));

// Start server
const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  
  // 设置每日统计数据更新任务
  schedule.scheduleJob('0 0 * * *', updateDailyStats);
});


