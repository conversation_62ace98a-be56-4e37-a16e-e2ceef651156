/**
 * Copyright 2025 Beijing Volcano Engine Technology Co., Ltd. All Rights Reserved.
 * SPDX-license-identifier: BSD-3-Clause
 */

/**
 * @brief Basic APIs - 更新为新的实时语音对话API
 */
export const BasicAPIs = [
  {
    action: 'getScenes',
    apiPath: '/api/scenes',
    method: 'get',
  },
] as const;

/**
 * @brief Realtime Dialog APIs - 实时语音对话API
 */
export const RealtimeDialogAPIs = [
  {
    action: 'StartSession',
    apiPath: '/api/start_session',
    method: 'post',
  },
] as const;

/**
 * @brief 保持兼容性的AIGC APIs
 */
export const AigcAPIs = [
  {
    action: 'StartVoiceChat',
    apiPath: '/api/start_session',
    method: 'post',
  },
  {
    action: 'StopVoiceChat',
    apiPath: '/api/stop_session',
    method: 'post',
  },
] as const;
