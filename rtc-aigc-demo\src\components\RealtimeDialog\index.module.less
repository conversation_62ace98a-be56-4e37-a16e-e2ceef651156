.container {
  width: 100%;
  height: 100%;
}

.card {
  height: 100%;
  
  :global(.arco-card-body) {
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

.status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  
  &.green {
    background-color: #00b42a;
  }
  
  &.orange {
    background-color: #ff7d00;
  }
  
  &.red {
    background-color: #f53f3f;
  }
  
  &.gray {
    background-color: #86909c;
  }
}

.alert {
  margin-bottom: 16px;
}

.sessionInfo {
  padding: 12px;
  background-color: #f7f8fa;
  border-radius: 6px;
  font-size: 12px;
}

.controls {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-bottom: 1px solid #e5e6eb;
}

.textInput {
  margin: 16px 0;
}

.eventLog {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px;
  
  .logContent {
    flex: 1;
    max-height: 200px;
    overflow-y: auto;
    padding: 12px;
    background-color: #f7f8fa;
    border-radius: 6px;
    margin-top: 8px;
    
    .logItem {
      margin-bottom: 4px;
      font-size: 12px;
      line-height: 1.4;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.instructions {
  margin-top: 16px;
  padding: 12px;
  background-color: #f0f9ff;
  border-radius: 6px;
  font-size: 12px;
  line-height: 1.6;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .controls {
    :global(.arco-space) {
      flex-wrap: wrap;
      justify-content: center;
    }
  }
  
  .textInput {
    :global(.arco-input-group) {
      display: flex;
      
      :global(.arco-input-wrapper) {
        flex: 1;
      }
      
      :global(.arco-btn) {
        flex-shrink: 0;
      }
    }
  }
  
  .eventLog .logContent {
    max-height: 150px;
  }
}
