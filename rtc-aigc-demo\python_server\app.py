#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
新的后端API服务，集成实时语音对话功能
"""

import asyncio
import json
import uuid
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_socketio import SocketIO, emit, disconnect
import threading
import queue
import time

# 导入实时语音对话相关模块
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'realtime_dialog', 'realtime_dialog'))

from realtime_dialog_client import RealtimeDialogClient
import config as dialog_config
import protocol

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
CORS(app, origins="*")
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# 全局变量存储活跃的对话会话
active_sessions: Dict[str, 'VoiceDialogSession'] = {}

@dataclass
class SessionConfig:
    """会话配置"""
    session_id: str
    user_id: str
    bot_name: str = "豆包"
    system_role: str = "你使用活泼灵动的女声，性格开朗，热爱生活。"
    speaking_style: str = "你的说话风格简洁明了，语速适中，语调自然。"

class VoiceDialogSession:
    """语音对话会话管理类"""
    
    def __init__(self, session_config: SessionConfig, socket_id: str):
        self.config = session_config
        self.socket_id = socket_id
        self.client: Optional[RealtimeDialogClient] = None
        self.is_running = False
        self.is_connected = False
        self.audio_queue = queue.Queue()
        self.receive_task: Optional[asyncio.Task] = None
        
    async def start(self):
        """启动对话会话"""
        try:
            # 创建WebSocket配置
            ws_config = dialog_config.ws_connect_config.copy()
            ws_config['headers']['X-Api-Connect-Id'] = str(uuid.uuid4())
            
            # 创建客户端
            self.client = RealtimeDialogClient(config=ws_config, session_id=self.config.session_id)
            
            # 连接到服务器
            await self.client.connect()
            self.is_connected = True
            self.is_running = True
            
            # 发送Hello消息
            await self.client.say_hello()
            
            # 启动接收循环
            self.receive_task = asyncio.create_task(self._receive_loop())
            
            logger.info(f"会话 {self.config.session_id} 启动成功")
            
        except Exception as e:
            logger.error(f"启动会话失败: {e}")
            await self.stop()
            raise
    
    async def _receive_loop(self):
        """接收服务器响应的循环"""
        try:
            while self.is_running and self.client:
                response = await self.client.receive_server_response()
                await self._handle_server_response(response)
        except asyncio.CancelledError:
            logger.info("接收任务已取消")
        except Exception as e:
            logger.error(f"接收消息错误: {e}")
            socketio.emit('error', {'message': f'接收消息错误: {e}'}, room=self.socket_id)
    
    async def _handle_server_response(self, response: Dict[str, Any]):
        """处理服务器响应"""
        if not response:
            return
            
        try:
            if response['message_type'] == 'SERVER_ACK' and isinstance(response.get('payload_msg'), bytes):
                # 音频数据
                audio_data = response['payload_msg']
                socketio.emit('audio_data', {
                    'data': audio_data.hex(),  # 转换为十六进制字符串传输
                    'length': len(audio_data)
                }, room=self.socket_id)
                
            elif response['message_type'] == 'SERVER_FULL_RESPONSE':
                event = response.get('event')
                payload_msg = response.get('payload_msg', {})
                
                # 发送事件到前端
                socketio.emit('dialog_event', {
                    'event': event,
                    'payload': payload_msg,
                    'session_id': response.get('session_id')
                }, room=self.socket_id)
                
                # 处理会话结束事件
                if event in [152, 153]:
                    logger.info(f"收到会话结束事件: {event}")
                    await self.stop()
                    
            elif response['message_type'] == 'SERVER_ERROR':
                error_msg = response.get('payload_msg', '未知错误')
                logger.error(f"服务器错误: {error_msg}")
                socketio.emit('error', {'message': f'服务器错误: {error_msg}'}, room=self.socket_id)
                
        except Exception as e:
            logger.error(f"处理服务器响应错误: {e}")
            socketio.emit('error', {'message': f'处理响应错误: {e}'}, room=self.socket_id)
    
    async def send_audio(self, audio_data: bytes):
        """发送音频数据"""
        if self.client and self.is_running:
            try:
                await self.client.task_request(audio_data)
            except Exception as e:
                logger.error(f"发送音频数据错误: {e}")
                raise
    
    async def send_text(self, text: str, start: bool = True, end: bool = True):
        """发送文本消息"""
        if self.client and self.is_running:
            try:
                await self.client.chat_tts_text(
                    is_user_querying=False,
                    start=start,
                    end=end,
                    content=text
                )
            except Exception as e:
                logger.error(f"发送文本消息错误: {e}")
                raise
    
    async def stop(self):
        """停止会话"""
        self.is_running = False
        
        if self.receive_task:
            self.receive_task.cancel()
            try:
                await self.receive_task
            except asyncio.CancelledError:
                pass
        
        if self.client and self.is_connected:
            try:
                await self.client.finish_session()
                await self.client.finish_connection()
                await self.client.close()
            except Exception as e:
                logger.error(f"关闭客户端错误: {e}")
        
        self.is_connected = False
        logger.info(f"会话 {self.config.session_id} 已停止")

# HTTP API 路由
@app.route('/api/scenes', methods=['GET'])
def get_scenes():
    """获取场景列表"""
    scenes = [{
        'scene': {
            'id': 'realtime_dialog',
            'name': '实时语音对话',
            'description': '基于豆包的实时语音对话功能',
            'botName': '豆包',
            'isInterruptMode': True,
            'isVision': False,
            'isScreenMode': False
        }
    }]
    
    return jsonify({
        'success': True,
        'data': {'scenes': scenes}
    })

@app.route('/api/start_session', methods=['POST'])
def start_session():
    """启动对话会话"""
    try:
        data = request.get_json()
        user_id = data.get('user_id', str(uuid.uuid4()))
        session_id = str(uuid.uuid4())
        
        return jsonify({
            'success': True,
            'data': {
                'session_id': session_id,
                'user_id': user_id,
                'message': '会话创建成功，请通过WebSocket连接进行对话'
            }
        })
    except Exception as e:
        logger.error(f"启动会话错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# WebSocket 事件处理
@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    logger.info(f"客户端连接: {request.sid}")
    emit('connected', {'message': '连接成功'})

@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接"""
    logger.info(f"客户端断开连接: {request.sid}")
    
    # 清理会话
    session_to_remove = None
    for session_id, session in active_sessions.items():
        if session.socket_id == request.sid:
            session_to_remove = session_id
            break
    
    if session_to_remove:
        session = active_sessions[session_to_remove]
        asyncio.run(session.stop())
        del active_sessions[session_to_remove]

@socketio.on('start_dialog')
def handle_start_dialog(data):
    """启动对话"""
    try:
        session_id = data.get('session_id', str(uuid.uuid4()))
        user_id = data.get('user_id', str(uuid.uuid4()))
        
        # 创建会话配置
        session_config = SessionConfig(
            session_id=session_id,
            user_id=user_id,
            bot_name=data.get('bot_name', '豆包'),
            system_role=data.get('system_role', '你使用活泼灵动的女声，性格开朗，热爱生活。'),
            speaking_style=data.get('speaking_style', '你的说话风格简洁明了，语速适中，语调自然。')
        )
        
        # 创建会话
        session = VoiceDialogSession(session_config, request.sid)
        active_sessions[session_id] = session
        
        # 在新线程中启动会话
        def start_session_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(session.start())
                emit('dialog_started', {'session_id': session_id, 'user_id': user_id})
            except Exception as e:
                logger.error(f"启动对话失败: {e}")
                emit('error', {'message': f'启动对话失败: {e}'})
                if session_id in active_sessions:
                    del active_sessions[session_id]
            finally:
                loop.close()
        
        thread = threading.Thread(target=start_session_async)
        thread.daemon = True
        thread.start()
        
    except Exception as e:
        logger.error(f"处理启动对话请求错误: {e}")
        emit('error', {'message': f'启动对话失败: {e}'})

@socketio.on('send_audio')
def handle_send_audio(data):
    """发送音频数据"""
    try:
        session_id = data.get('session_id')
        audio_hex = data.get('audio_data')
        
        if not session_id or session_id not in active_sessions:
            emit('error', {'message': '会话不存在'})
            return
        
        if not audio_hex:
            emit('error', {'message': '音频数据为空'})
            return
        
        # 将十六进制字符串转换为字节
        audio_data = bytes.fromhex(audio_hex)
        
        session = active_sessions[session_id]
        
        # 在新线程中发送音频
        def send_audio_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(session.send_audio(audio_data))
            except Exception as e:
                logger.error(f"发送音频错误: {e}")
                emit('error', {'message': f'发送音频失败: {e}'})
            finally:
                loop.close()
        
        thread = threading.Thread(target=send_audio_async)
        thread.daemon = True
        thread.start()
        
    except Exception as e:
        logger.error(f"处理音频数据错误: {e}")
        emit('error', {'message': f'处理音频数据失败: {e}'})

@socketio.on('send_text')
def handle_send_text(data):
    """发送文本消息"""
    try:
        session_id = data.get('session_id')
        text = data.get('text')
        start = data.get('start', True)
        end = data.get('end', True)
        
        if not session_id or session_id not in active_sessions:
            emit('error', {'message': '会话不存在'})
            return
        
        if not text:
            emit('error', {'message': '文本内容为空'})
            return
        
        session = active_sessions[session_id]
        
        # 在新线程中发送文本
        def send_text_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(session.send_text(text, start, end))
            except Exception as e:
                logger.error(f"发送文本错误: {e}")
                emit('error', {'message': f'发送文本失败: {e}'})
            finally:
                loop.close()
        
        thread = threading.Thread(target=send_text_async)
        thread.daemon = True
        thread.start()
        
    except Exception as e:
        logger.error(f"处理文本消息错误: {e}")
        emit('error', {'message': f'处理文本消息失败: {e}'})

@socketio.on('stop_dialog')
def handle_stop_dialog(data):
    """停止对话"""
    try:
        session_id = data.get('session_id')
        
        if not session_id or session_id not in active_sessions:
            emit('error', {'message': '会话不存在'})
            return
        
        session = active_sessions[session_id]
        
        # 在新线程中停止会话
        def stop_session_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(session.stop())
                emit('dialog_stopped', {'session_id': session_id})
            except Exception as e:
                logger.error(f"停止对话错误: {e}")
                emit('error', {'message': f'停止对话失败: {e}'})
            finally:
                loop.close()
        
        thread = threading.Thread(target=stop_session_async)
        thread.daemon = True
        thread.start()
        
        # 从活跃会话中移除
        del active_sessions[session_id]
        
    except Exception as e:
        logger.error(f"处理停止对话请求错误: {e}")
        emit('error', {'message': f'停止对话失败: {e}'})

if __name__ == '__main__':
    logger.info("启动实时语音对话服务器...")
    socketio.run(app, host='0.0.0.0', port=3001, debug=True)
