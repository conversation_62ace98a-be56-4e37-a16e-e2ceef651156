# 实时语音对话系统

本项目将原有的RTC-AIGC演示项目与实时语音对话API进行了融合，创建了一个完整的实时语音对话系统。

## 系统架构

### 后端服务 (Python)
- **位置**: `python_server/`
- **技术栈**: Flask + Flask-SocketIO + WebSocket
- **功能**: 
  - 集成实时语音对话API
  - 提供WebSocket接口供前端连接
  - 处理音频数据的实时传输
  - 管理对话会话生命周期

### 前端应用 (React)
- **位置**: `src/`
- **技术栈**: React + TypeScript + Socket.IO Client
- **功能**:
  - 实时语音录制和播放
  - WebSocket连接管理
  - 用户界面交互
  - 音频数据处理

## 安装和配置

### 1. 安装Python依赖

```bash
cd python_server
pip install -r requirements.txt
```

### 2. 安装前端依赖

```bash
npm install
```

### 3. 配置API密钥

编辑 `realtime_dialog/realtime_dialog/config.py` 文件，确保以下配置正确：

```python
ws_connect_config = {
    "base_url": "wss://openspeech.bytedance.com/api/v3/realtime/dialogue",
    "headers": {
        "X-Api-App-ID": "你的APP_ID",
        "X-Api-Access-Key": "你的ACCESS_KEY",
        "X-Api-Resource-Id": "volc.speech.dialog",
        "X-Api-App-Key": "你的APP_KEY",
        "X-Api-Connect-Id": str(uuid.uuid4()),
    }
}
```

## 启动系统

### 快速测试（推荐先运行）

```bash
python quick_test.py
```

### 方式1: 使用启动脚本

**Linux/Mac:**
```bash
python start_all.py
```

**Windows:**
```batch
# 启动后端
start_backend.bat

# 启动前端（新窗口）
start_frontend.bat
```

### 方式2: 使用npm脚本

```bash
# 启动后端
npm run python-server:start

# 启动前端（新终端）
npm start
```

### 方式3: 分别启动

**启动后端服务器:**
```bash
cd python_server
python start.py
```

**启动前端开发服务器:**
```bash
npm start
```

## 使用说明

### 1. 访问应用
打开浏览器访问: `http://localhost:3000`

### 2. 选择场景
在场景列表中选择 "实时语音对话" 场景

### 3. 开始对话
1. 点击 "连接服务器" 建立WebSocket连接
2. 点击 "开始对话" 启动语音对话会话
3. 点击 "开始录音" 进行语音输入
4. 或者直接在文本框中输入文字消息
5. AI会实时响应并播放语音回复

### 4. 功能特性
- **实时语音识别**: 支持实时语音输入和识别
- **语音合成播放**: AI回复会自动转换为语音播放
- **文本输入**: 支持文本输入作为语音输入的补充
- **事件监控**: 实时显示对话过程中的各种事件
- **连接状态**: 清晰显示连接和对话状态

## API接口

### HTTP接口

#### GET /api/scenes
获取可用场景列表

#### POST /api/start_session
启动对话会话

### WebSocket事件

#### 客户端发送事件
- `start_dialog`: 启动对话
- `stop_dialog`: 停止对话
- `send_audio`: 发送音频数据
- `send_text`: 发送文本消息

#### 服务器发送事件
- `connected`: 连接确认
- `dialog_started`: 对话已启动
- `dialog_stopped`: 对话已停止
- `audio_data`: 音频数据
- `dialog_event`: 对话事件
- `error`: 错误信息

## 文件结构

```
rtc-aigc-demo/
├── python_server/              # Python后端服务
│   ├── app.py                 # 主应用文件
│   ├── start.py               # 启动脚本
│   └── requirements.txt       # Python依赖
├── src/
│   ├── components/
│   │   └── RealtimeDialog/    # 实时对话组件
│   ├── lib/
│   │   ├── RealtimeDialogClient.ts  # WebSocket客户端
│   │   ├── AudioManager.ts          # 音频管理器
│   │   └── useRealtimeDialog.ts     # React Hook
│   └── pages/MainPage/MainArea/Room/
│       └── index.tsx          # 房间组件（已修改）
├── realtime_dialog/           # 实时语音对话API
└── start_all.py              # 系统启动脚本
```

## 技术细节

### 音频处理
- **输入格式**: PCM 16kHz 16bit 单声道
- **输出格式**: PCM 24kHz 32bit 单声道
- **传输格式**: 十六进制字符串（通过WebSocket）

### WebSocket通信
- **协议**: Socket.IO
- **传输**: 实时双向通信
- **数据格式**: JSON + 十六进制音频数据

### 会话管理
- **会话ID**: 自动生成唯一标识
- **生命周期**: 连接 -> 启动对话 -> 音频交互 -> 停止对话 -> 断开连接
- **错误处理**: 完整的错误捕获和用户提示

## 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查后端服务是否正常启动
   - 确认端口3001没有被占用
   - 检查防火墙设置

2. **音频录制失败**
   - 确认浏览器已授予麦克风权限
   - 检查音频设备是否正常工作
   - 尝试刷新页面重新授权

3. **API调用失败**
   - 检查 `config.py` 中的API密钥配置
   - 确认网络连接正常
   - 查看后端日志获取详细错误信息

### 调试模式

启动时添加调试参数：
```bash
cd python_server
python start.py --debug
```

## 开发说明

### 添加新功能
1. 后端: 在 `python_server/app.py` 中添加新的WebSocket事件处理
2. 前端: 在相应的React组件中添加新的UI和逻辑

### 自定义配置
- 修改 `realtime_dialog/realtime_dialog/config.py` 调整对话参数
- 修改 `src/components/RealtimeDialog/index.tsx` 调整UI界面

## 许可证

本项目基于原有的BSD-3-Clause许可证。
