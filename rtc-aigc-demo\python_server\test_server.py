#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试脚本 - 验证后端服务功能
"""

import requests
import json
import time
import socketio

def test_http_api():
    """测试HTTP API"""
    print("=== 测试HTTP API ===")
    
    base_url = "http://localhost:3001"
    
    try:
        # 测试获取场景列表
        print("1. 测试获取场景列表...")
        response = requests.get(f"{base_url}/api/scenes")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 场景列表获取成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"✗ 场景列表获取失败: {response.status_code}")
            
        # 测试启动会话
        print("\n2. 测试启动会话...")
        response = requests.post(f"{base_url}/api/start_session", json={
            "user_id": "test_user_123"
        })
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 会话启动成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data.get('data', {}).get('session_id')
        else:
            print(f"✗ 会话启动失败: {response.status_code}")
            
    except Exception as e:
        print(f"✗ HTTP API测试失败: {e}")
        
    return None

def test_websocket():
    """测试WebSocket连接"""
    print("\n=== 测试WebSocket连接 ===")
    
    try:
        # 创建Socket.IO客户端
        sio = socketio.Client()
        
        # 连接事件
        @sio.event
        def connect():
            print("✓ WebSocket连接成功")
            
        @sio.event
        def disconnect():
            print("✓ WebSocket连接断开")
            
        @sio.event
        def connected(data):
            print(f"✓ 服务器确认连接: {data}")
            
        @sio.event
        def dialog_started(data):
            print(f"✓ 对话启动成功: {data}")
            
        @sio.event
        def dialog_stopped(data):
            print(f"✓ 对话停止成功: {data}")
            
        @sio.event
        def audio_data(data):
            print(f"✓ 收到音频数据: {len(data.get('data', ''))} 字符")
            
        @sio.event
        def dialog_event(data):
            print(f"✓ 收到对话事件: {data}")
            
        @sio.event
        def error(data):
            print(f"✗ 收到错误: {data}")
        
        # 连接到服务器
        print("1. 连接到WebSocket服务器...")
        sio.connect('http://localhost:3001')
        
        # 等待连接建立
        time.sleep(1)
        
        # 测试启动对话
        print("2. 测试启动对话...")
        sio.emit('start_dialog', {
            'session_id': 'test_session_123',
            'user_id': 'test_user_123',
            'bot_name': '豆包'
        })
        
        # 等待响应
        time.sleep(2)
        
        # 测试发送文本
        print("3. 测试发送文本消息...")
        sio.emit('send_text', {
            'session_id': 'test_session_123',
            'text': '你好，这是一个测试消息'
        })
        
        # 等待响应
        time.sleep(3)
        
        # 测试停止对话
        print("4. 测试停止对话...")
        sio.emit('stop_dialog', {
            'session_id': 'test_session_123'
        })
        
        # 等待响应
        time.sleep(1)
        
        # 断开连接
        sio.disconnect()
        
        print("✓ WebSocket测试完成")
        
    except Exception as e:
        print(f"✗ WebSocket测试失败: {e}")

def test_audio_processing():
    """测试音频处理"""
    print("\n=== 测试音频处理 ===")
    
    try:
        # 创建测试音频数据（模拟PCM数据）
        test_audio = b'\x00\x01\x02\x03' * 100  # 400字节的测试数据
        hex_audio = test_audio.hex()
        
        print(f"1. 创建测试音频数据: {len(test_audio)} 字节")
        print(f"2. 转换为十六进制: {len(hex_audio)} 字符")
        
        # 测试十六进制转换
        converted_back = bytes.fromhex(hex_audio)
        if converted_back == test_audio:
            print("✓ 音频数据转换测试通过")
        else:
            print("✗ 音频数据转换测试失败")
            
    except Exception as e:
        print(f"✗ 音频处理测试失败: {e}")

def main():
    """主测试函数"""
    print("开始测试后端服务...")
    print("请确保后端服务已启动 (python start.py)")
    print("=" * 50)
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    # 测试HTTP API
    session_id = test_http_api()
    
    # 测试WebSocket
    test_websocket()
    
    # 测试音频处理
    test_audio_processing()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    
    # 提供使用建议
    print("\n使用建议:")
    print("1. 如果HTTP API测试失败，请检查Flask服务是否正常启动")
    print("2. 如果WebSocket测试失败，请检查Socket.IO服务是否正常")
    print("3. 如果音频处理测试失败，请检查音频转换逻辑")
    print("4. 确保实时对话API的配置正确 (config.py)")

if __name__ == "__main__":
    main()
