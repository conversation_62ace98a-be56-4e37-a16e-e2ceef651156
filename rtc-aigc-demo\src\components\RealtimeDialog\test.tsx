/**
 * 实时语音对话组件测试页面
 */

import React from 'react';
import RealtimeDialog from './index';

const TestRealtimeDialog: React.FC = () => {
  return (
    <div style={{ padding: '20px', height: '100vh' }}>
      <h1>实时语音对话组件测试</h1>
      <div style={{ height: 'calc(100vh - 100px)' }}>
        <RealtimeDialog 
          autoConnect={false}
          autoStartRecording={false}
          botName="豆包测试"
        />
      </div>
    </div>
  );
};

export default TestRealtimeDialog;
